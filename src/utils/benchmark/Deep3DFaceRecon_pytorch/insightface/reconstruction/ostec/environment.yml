channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - aiocontextvars=0.2.2=py_0
  - alsa-lib=1.2.3=h516909a_0
  - apptools=5.1.0=pyh44b312d_0
  - bzip2=1.0.8=h7f98852_4
  - c-ares=1.17.1=h7f98852_1
  - ca-certificates=2021.9.30=h06a4308_1
  - cairo=1.16.0=h6cf1ce9_1008
  - certifi=2020.12.5=py36h06a4308_0
  - cloudpickle=2.0.0=pyhd8ed1ab_0
  - configobj=5.0.6=py_0
  - contextvars=2.4=py_0
  - curl=7.78.0=hea6ffbf_0
  - cycler=0.10.0=py_2
  - cytoolz=0.11.0=py36h8f6f2f9_3
  - cyvlfeat=0.7.0=py36h0280710_0
  - dask-core=2.25.0=py_0
  - dbus=1.13.6=h48d8840_2
  - decorator=4.4.2=py_0
  - double-conversion=3.1.5=h9c3ff4c_2
  - eigen=3.3.9=h4bd325d_1
  - enum34=1.1.10=py36h9f0ad1d_2
  - envisage=6.0.1=pyhd8ed1ab_0
  - expat=2.4.1=h9c3ff4c_0
  - ffmpeg=4.3.2=hca11adc_0
  - fontconfig=2.13.1=hba837de_1005
  - freetype=2.10.4=h0708190_1
  - gettext=********=h0b5b191_1005
  - gl2ps=1.4.2=h0708190_0
  - glcontext=2.3.4=py36hc4f0c31_0
  - glew=2.1.0=h9c3ff4c_2
  - glib=2.68.3=h9c3ff4c_0
  - glib-tools=2.68.3=h9c3ff4c_0
  - gmp=6.2.1=h58526e2_0
  - gnutls=3.6.13=h85f3911_1
  - graphite2=1.3.13=h58526e2_1001
  - gst-plugins-base=1.18.4=hf529b03_2
  - gstreamer=1.18.4=h76c114f_2
  - h5py=2.10.0=py36hd6299e0_1
  - harfbuzz=2.8.2=h83ec7ef_0
  - hdf4=4.2.15=h10796ff_3
  - hdf5=1.10.6=nompi_h6a2412b_1114
  - icu=68.1=h58526e2_0
  - imagecodecs-lite=2019.12.3=py36h92226af_3
  - imageio=2.9.0=py_0
  - immutables=0.15=py36h8f6f2f9_0
  - importlib-metadata=4.8.1=py36h5fab9bb_0
  - importlib_metadata=4.8.1=hd8ed1ab_0
  - importlib_resources=5.2.2=pyhd8ed1ab_0
  - jasper=1.900.1=h07fcdf6_1006
  - jbig=2.1=h7f98852_2003
  - jpeg=9d=h36c2ea0_0
  - jsoncpp=1.9.4=h4bd325d_3
  - kiwisolver=1.3.1=py36h605e78d_1
  - krb5=1.19.2=hcc1bbae_0
  - lame=3.100=h7f98852_1001
  - lcms2=2.12=hddcbb42_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - lerc=2.2.1=h9c3ff4c_0
  - libblas=3.9.0=11_linux64_openblas
  - libcblas=3.9.0=11_linux64_openblas
  - libclang=11.1.0=default_ha53f305_1
  - libcurl=7.78.0=h2574ce0_0
  - libdeflate=1.7=h7f98852_5
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=h516909a_1
  - libevent=2.1.10=hcdb4288_3
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=11.2.0=h69a702a_11
  - libgfortran5=11.2.0=h5c6108e_11
  - libglib=2.68.3=h3e27bee_0
  - libglu=9.0.0=he1b5a44_1001
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.16=h516909a_0
  - liblapack=3.9.0=11_linux64_openblas
  - liblapacke=3.9.0=11_linux64_openblas
  - libllvm11=11.1.0=hf817b99_2
  - libnetcdf=4.8.0=nompi_hcd642e3_103
  - libnghttp2=1.43.0=h812cca2_0
  - libogg=1.3.4=h7f98852_1
  - libopenblas=0.3.17=pthreads_h8fe5266_1
  - libopencv=4.5.2=py36hb84549a_1
  - libopus=1.3.1=h7f98852_1
  - libpng=1.6.37=h21135ba_2
  - libpq=13.3=hd57d9b9_0
  - libprotobuf=3.16.0=h780b84a_0
  - libssh2=1.9.0=ha56f1ee_6
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtheora=1.1.1=h7f98852_1005
  - libtiff=4.3.0=hf544144_1
  - libuuid=2.32.1=h7f98852_1000
  - libvorbis=1.3.7=h9c3ff4c_0
  - libwebp-base=1.2.0=h7f98852_2
  - libxcb=1.13=h7f98852_1003
  - libxkbcommon=1.0.3=he3ba5ed_0
  - libxml2=2.9.12=h72842e0_0
  - libzip=1.8.0=h4de3113_0
  - loguru=0.5.3=py36h5fab9bb_2
  - lz4-c=1.9.3=h9c3ff4c_1
  - menpo=0.11.0=py36h355b2fd_0
  - menpo3d=0.8.3=py36h355b2fd_0
  - moderngl=5.6.4=py36h284efc9_0
  - mysql-common=8.0.25=ha770c72_2
  - mysql-libs=8.0.25=hfa10184_2
  - ncurses=6.2=he6710b0_1
  - nettle=3.6=he412f7d_0
  - networkx=2.5.1=pyhd8ed1ab_0
  - nspr=4.30=h9c3ff4c_0
  - nss=3.67=hb5efdd6_0
  - olefile=0.46=pyh9f0ad1d_1
  - opencv=4.5.2=py36h5fab9bb_1
  - openh264=2.1.1=h780b84a_0
  - openjpeg=2.4.0=hb52868f_1
  - openssl=1.1.1l=h7f8727e_0
  - pathlib=1.0.1=py36h5fab9bb_4
  - pcre=8.45=h9c3ff4c_0
  - pillow=8.3.1=py36h676a545_0
  - pip=21.0.1=py36h06a4308_0
  - pixman=0.40.0=h36c2ea0_0
  - proj=7.2.0=h277dcde_2
  - pthread-stubs=0.4=h36c2ea0_1001
  - pugixml=1.11.4=h9c3ff4c_0
  - py-opencv=4.5.2=py36hcb3619a_1
  - pyface=7.3.0=pyh44b312d_1
  - pygments=2.10.0=pyhd8ed1ab_0
  - pyparsing=2.4.7=pyh9f0ad1d_0
  - pyqt=5.12.3=py36h5fab9bb_7
  - pyqt-impl=5.12.3=py36h7ec31b9_7
  - pyqt5-sip=4.19.18=py36hc4f0c31_7
  - pyqtchart=5.12=py36h7ec31b9_7
  - pyqtwebengine=5.12.1=py36h7ec31b9_7
  - python=3.6.13=h12debd9_1
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.6=2_cp36m
  - pywavelets=1.1.1=py36h92226af_3
  - qt=5.12.9=hda022c4_4
  - readline=8.1=h27cfd23_0
  - scikit-image=0.17.2=py36h284efc9_4
  - scipy=1.5.3=py36h9e8f40b_0
  - setuptools=58.0.4=py36h06a4308_0
  - six=1.16.0=pyh6c4a22f_0
  - sqlite=3.36.0=hc218d9a_0
  - tbb=2020.2=h4bd325d_4
  - tbb-devel=2020.2=h4bd325d_4
  - tifffile=2019.7.26.2=py36_0
  - tk=8.6.11=h1ccaba5_0
  - toolz=0.11.1=py_0
  - tornado=6.1=py36h8f6f2f9_1
  - traits=6.2.0=py36h8f6f2f9_0
  - traitsui=7.2.0=pyhd8ed1ab_0
  - typing_extensions=********=pyha770c72_0
  - utfcpp=3.2.1=ha770c72_0
  - vlfeat=0.9.20=h14c3975_1002
  - vtk=9.0.1=no_osmesa_py36hfa3a401_109
  - wheel=0.37.0=pyhd3eb1b0_1
  - x264=1!161.3030=h7f98852_1
  - xorg-kbproto=1.0.7=h7f98852_1002
  - xorg-libice=1.0.10=h7f98852_0
  - xorg-libsm=1.2.3=hd9c2040_1000
  - xorg-libx11=1.7.2=h7f98852_0
  - xorg-libxau=1.0.9=h7f98852_0
  - xorg-libxdmcp=1.1.3=h7f98852_0
  - xorg-libxext=1.3.4=h7f98852_1
  - xorg-libxrender=0.9.10=h7f98852_1003
  - xorg-libxt=1.2.1=h7f98852_2
  - xorg-renderproto=0.11.1=h7f98852_1002
  - xorg-xextproto=7.3.0=h7f98852_1002
  - xorg-xproto=7.0.31=h7f98852_1007
  - xz=5.2.5=h7b6447c_0
  - yaml=0.2.5=h516909a_0
  - zipp=3.6.0=pyhd8ed1ab_0
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.5.0=ha95c52a_0
  - pip:
    - absl-py==0.14.1
    - astor==0.8.1
    - cached-property==1.5.2
    - charset-normalizer==2.0.7
    - cmake==3.21.3
    - dataclasses==0.8
    - dlib==19.22.1
    - dominate==2.6.0
    - future==0.18.2
    - gast==0.5.2
    - google-pasta==0.2.0
    - grpcio==1.41.0
    - idna==3.3
    - imutils==0.5.4
    - keras==2.3.0
    - keras-applications==1.0.8
    - keras-preprocessing==1.0.5
    - kornia==0.5.5
    - markdown==3.3.4
    - matplotlib==3.1.3
    - mayavi==4.7.2
    - numpy==1.16.1
    - protobuf==3.18.1
    - pyyaml==6.0
    - requests==2.26.0
    - tensorboard==1.14.0
    - tensorflow-estimator==1.14.0
    - tensorflow-gpu==1.14.0
    - termcolor==1.1.0
    - torch==1.6.0
    - torchvision==0.7.0
    - tqdm==4.62.3
    - trimesh==3.9.20
    - urllib3==1.26.7
    - werkzeug==2.0.2
    - wrapt==1.13.2
