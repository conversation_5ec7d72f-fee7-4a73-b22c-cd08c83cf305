train{
    expname = test
    dataset_class = datasets.dataset.IFDataset
    model_class = model.renderer.IFNetwork
    loss_class = model.loss.IFLoss
    learning_rate = 1.0e-4
    num_pixels = 2048
    plot_freq = 100
    alpha_milestones = [250, 500, 750, 1000, 1250]
    alpha_factor = 2
    sched_milestones = [1000,1500]
    sched_factor = 0.5
}
plot{
    plot_nimgs = 1
    max_depth = 3.0
    resolution = 100
}
loss{
    eikonal_weight = 0.1
    mask_weight = 100.0
    reg_weight = 5.0
    normal_weight = 1.0
    alpha = 50.0
}
dataset{
    data_dir = Test
    img_res = [1024, 1024]
    scan_id = 0
}
model{
    feature_vector_size = 256
    implicit_network
    {
        d_in = 3
        d_out = 1
        dims = [512, 512, 512, 512, 512, 512, 512, 512]
        geometric_init = True
        bias = 0.6
        skip_in = [4]
        weight_norm = True
        multires = 6
    }
    diffuse_network
    {
        dims = [128]
        weight_norm = True
        multires_view = 6
    }
    specular_network
    {
        dims = [128]
        weight_norm = True
        multires_view = 4
    }
    albedo_network
    {
        dims = [256, 256, 256, 256]
        weight_norm = True
        multires_view = 4
    }
    ray_tracer
    {
        object_bounding_sphere = 1.0
        sdf_threshold = 5.0e-5
        line_search_step = 0.5
        line_step_iters = 3
        sphere_tracing_iters = 10
        n_steps = 100
        n_secant_steps = 8
    }
}