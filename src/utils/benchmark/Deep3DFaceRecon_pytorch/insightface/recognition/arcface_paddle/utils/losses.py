# Copyright (c) 2021 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


class CosFace(object):
    def __init__(self, m1=1.0, m2=0.0, m3=0.35, s=64.0):
        super(CosFace, self).__init__()
        self.margin1 = m1
        self.margin2 = m2
        self.margin3 = m3
        self.scale = s


class ArcFace(object):
    def __init__(self, m1=1.0, m2=0.5, m3=0.0, s=64.0):
        super(ArcFace, self).__init__()
        self.margin1 = m1
        self.margin2 = m2
        self.margin3 = m3
        self.scale = s


class SphereFace(object):
    def __init__(self, m1=1.35, m2=0.0, m3=0.0, s=64.0):
        super(SphereFace, self).__init__()
        self.margin1 = m1
        self.margin2 = m2
        self.margin3 = m3
        self.scale = s
