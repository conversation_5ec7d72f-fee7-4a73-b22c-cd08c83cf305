# PaddleServing预测功能测试

PaddleServing预测功能测试的主程序为`test_serving.sh`，可以测试基于PaddleServing的部署功能。

## 1. 测试结论汇总

本repo未提供量化训练，因此这里只测试正常模型，对应的PaddleServing预测功能汇总如下：

| 模型类型 |device | batchsize | tensorrt | mkldnn | cpu多线程 |
|  :----:   |  :----: |   :----:   |  :----:  |   :----:   |  :----:  |
| 正常模型 | GPU | 1/6 | fp32 | - | - |
| 正常模型 | CPU | 1/6 | - | fp32 | 支持 |

## 2. 测试流程
### 2.1 功能测试
先运行`prepare.sh`准备数据和模型，然后运行`test_serving.sh`进行测试，最终在```test_tipc/output```目录下生成`serving_infer_*.log`后缀的日志文件。

```shell
bash test_tipc/prepare.sh ./test_tipc/configs/ms1mv2_mobileface/model_linux_gpu_normal_normal_serving_python_linux_gpu_cpu.txt "serving_infer"

# 用法:
bash test_tipc/test_serving.sh ./test_tipc/configs//ms1mv2_mobileface/model_linux_gpu_normal_normal_serving_python_linux_gpu_cpu.txt
```  

#### 运行结果

各测试的运行情况会打印在 `test_tipc/output/results_serving.log` 中：
运行成功时会输出：

```
Run successfully with command - python3.7 pipeline_http_client.py --image_dir=./imgs > ../../test_tipc/output/server_infer_cpu_usemkldnn_True_threads_1_batchsize_1.log 2>&1 ! 
Run successfully  with command - xxxxx
...
```

运行失败时会输出：

```
Run failed with command - python3.7 pipeline_http_client.py --image_dir=./imgs > ../../test_tipc/output/server_infer_cpu_usemkldnn_True_threads_1_batchsize_1.log 2>&1 !
Run failed with command - xxxxx
...
```

详细的预测结果会存在 test_tipc/output/ 文件夹下，例如`server_infer_gpu_usetrt_True_precision_fp32_batchsize_1.log`中会返回图像的特征值:

```
{'err_no': 0, 'err_msg': '', 'key': ['out'], 'value': ['array([[ 1.36603206e-01, -2.12395296e-01, -3.94680113e-01,\n        -3.14380080e-01, -9.66617092e-03,  1.87318385e-01,\n        -2.97903419e-01, -3.17218006e-01, -2.69029588e-01,\n         1.21175185e-01, -1.90171480e-01,  5.15628010e-02,\n        -1.09966584e-01, -2.23269954e-01,  5.43062799e-02,\n        -1.33496851e-01, -1.19007424e-01,  2.22256035e-01,\n        -2.77910858e-01,  2.71745831e-01, -4.16789412e-01,\n        -2.12772295e-01,  7.03845620e-01, -6.93172514e-02,\n         1.72736168e-01, -2.60139287e-01,  3.03129200e-03,\n         2.11665437e-01, -1.58136543e-02, -2.38662288e-02,\n        -5.83377741e-02,  5.22087336e-01,  2.94472545e-01,\n         1.68193743e-01, -7.54145905e-02,  1.43897519e-01,\n         1.56238422e-01, -3.39259744e-01,  2.46101081e-01,\n         3.11530419e-02, -5.94105422e-01, -2.72643536e-01,\n         1.21330276e-01,  3.12743425e-01, -1.66200623e-01,\n        -6.53145928e-03, -2.84941733e-01,  5.59734181e-05,\n        -3.21606755e-01, -1.73298046e-01, -1.07766673e-01,\n         9.54522491e-02,  2.46445552e-01, -2.62605727e-01,\n         1.81617990e-01,  6.52089193e-02, -1.01563215e-01,\n         3.59104156e-01, -5.22237360e-01,  1.64726060e-02,\n        -3.69388551e-01, -4.39793877e-02, -1.99547961e-01,\n        -3.79198231e-03,  3.00050706e-01, -1.49292305e-01,\n        -1.96511611e-01, -4.50382173e-01,  4.40837264e-01,\n        -2.56556179e-02, -1.36169955e-01, -3.62343282e-01,\n         1.56754032e-02,  7.93581456e-02,  1.90513626e-01,\n        -3.41799140e-01, -5.37522621e-02,  2.99514532e-01,\n        -1.21103093e-01,  4.06056821e-01, -2.30544969e-01,\n        -1.08799607e-01, -1.23380020e-01, -1.04779311e-01,\n        -3.59124064e-01,  6.79017082e-02,  3.27649474e-01,\n        -1.09562665e-01,  1.78656310e-01, -2.54520983e-01,\n        -2.15707019e-01,  2.97523111e-01, -3.25083762e-01,\n         1.63179748e-02,  3.89623255e-01, -7.29642585e-02,\n         5.47815263e-01, -9.16893259e-02, -4.76058573e-01,\n        -1.75076187e-01, -1.13026705e-04,  2.48254672e-01,\n         3.72678041e-01, -4.53566402e-01,  6.30904138e-02,\n         5.19643247e-01, -1.70341924e-01, -5.24724603e-01,\n        -9.19980407e-02,  5.36089689e-02,  9.92866978e-02,\n         2.93649197e-01,  1.39556557e-01,  4.84964341e-01,\n         3.11437190e-01,  3.61269027e-01, -3.84658389e-02,\n        -1.26146287e-01, -2.82240808e-01, -6.71329573e-02,\n        -5.25959721e-03,  2.54376501e-01, -2.77014941e-01,\n        -4.57646847e-02, -1.97771654e-01, -2.70207506e-02,\n         9.59944800e-02, -1.04830116e-02]], dtype=float32)']}
```


## 3. 更多教程

本文档为功能测试用，更详细的Serving预测使用教程请参考：[ArcFace 服务化部署](../../deploy/pdserving/README_CN.md)  
