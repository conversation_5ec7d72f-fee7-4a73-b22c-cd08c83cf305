## Face Recognition


<div align="left">
  <img src="https://insightface.ai/assets/img/custom/logo3.jpg" width="240"/>
</div>


## Introduction

These are the face recognition methods of [InsightFace](https://insightface.ai)


<div align="left">
  <img src="https://insightface.ai/assets/img/github/facerecognitionfromvideo.PNG" width="600"/>
</div>


### Datasets

  Please refer to [datasets](_datasets_) page for the details of face recognition datasets used for training and evaluation.

### Evaluation

  Please refer to [evaluation](_evaluation_) page for the details of face recognition evaluation.


## Methods


Supported methods:

- [x] [ArcFace_torch (CVPR'2019)](arcface_torch)
- [x] [ArcFace_mxnet (CVPR'2019)](arcface_mxnet)
- [x] [ArcFace_Paddle (CVPR'2019)](arcface_paddle)
- [x] [Arcface_oneflow](arcface_oneflow)
- [x] [SubCenter ArcFace (ECCV'2020)](subcenter_arcface)
- [x] [VPL (CVPR'2021)](vpl)
- [x] [PartialFC_torch (CVPR'2022)](arcface_torch)
- [x] [PartialFC_mxnet (CVPR'2022)](partial_fc)
- [x] [IDMMD (NeurIPS'2022)](https://github.com/deepinsight/insightface/tree/master/recognition/idmmd)


## Contributing

We appreciate all contributions to improve the face recognition model zoo of InsightFace. 


