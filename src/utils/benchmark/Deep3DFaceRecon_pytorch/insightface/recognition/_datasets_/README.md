# Face Recognition Datasets

## Training Datasets (Updating)

### CASIA-Webface (10K ids/0.5M images) [1]

[Baidu](https://pan.baidu.com/s/1AfHdPsxJZBD8kBJeIhmq1w)

[GDrive](https://drive.google.com/file/d/1KxNCrXzln0lal3N4JiYl9cFOIhT78y1l/view?usp=sharing)

### CelebA (10K ids/0.2M images) [2]

### UMDFace (8K ids/0.37M images) [3]

[Baidu](https://pan.baidu.com/s/1aGutJwNWpV-lA0f_7eNsGQ)

[GDrive](https://drive.google.com/file/d/1azhEHoJjVmifuzBVKJwl-sDbLZ-Wzp4O/view?usp=sharing)

### VGG2 (9K ids/3.31M images) [4]

[Baidu](https://pan.baidu.com/s/1c3KeLzy)

[GDrive](https://drive.google.com/file/d/1dyVQ7X3d28eAcjV3s3o0MT-HyODp_v3R/view?usp=sharing)

### MS1M-IBUG (85K ids/3.8M images) [5,6]

[Baidu](https://pan.baidu.com/s/1nxmSCch)

### MS1M-ArcFace (85K ids/5.8M images) [5,7] 

[Baidu](https://pan.baidu.com/s/1S6LJZGdqcZRle1vlcMzHOQ)

[GDrive](https://drive.google.com/file/d/1SXS4-Am3bsKSK615qbYdbA_FMVh3sAvR/view?usp=sharing)

### MS1M-RetinaFace

[Baidu](https://pan.baidu.com/s/1RBnaW88PC6cKqtYwgfVX8Q) (code:8eb3)

[GDrive](https://drive.google.com/file/d/1JgmzL9OLTqDAZE86pBgETtSQL4USKTFy/view?usp=sharing)

### Asian-Celeb (94K ids/2.8M images)[8]

[Baidu](https://pan.baidu.com/s/12wSgofDy1flFf6lOyAxJRg)

### Glint360K (360K ids/17M images)[17]

[Baidu](https://pan.baidu.com/s/1GsYqTTt7_Dn8BfxxsLFN0w) (code:o3az)    
magnet uri: `magnet:?xt=urn:btih:E5F46EE502B9E76DA8CC3A0E4F7C17E4000C7B1E&dn=glint360k`

### Glint-Mini (91K ids/5.2M images)[17]
[Baidu](https://pan.baidu.com/s/10IzEyP-Z9dWFcxxj9jdJpQ) (code:10m5)

### DeepGlint (181K ids/6.75M images) [8] 

[baidu](https://pan.baidu.com/s/1yApUbklBgRgOyOV4o3J8Eg)

### WebFace260M [18]

[Link](https://www.face-benchmark.org/download.html)


### IMDB-Face (59K ids/1.7M images) [9]

### Celeb500k (500K ids/50M images) [10]

### MegaFace(train) (672K ids/4.7M images) [11]

[Baidu](https://pan.baidu.com/s/1uy366DjUiGc3AvhuamRLyw) (code:5f8m)

[GDrive](https://drive.google.com/file/d/1O4FxijSXoEIe6fLfOocqF4VFMh5B4d89/view?usp=sharing)

### DigiFace-1M (110K ids/1.22M images) [19]

[Website](https://microsoft.github.io/DigiFace1M/)

[Github](https://github.com/microsoft/DigiFace1M)

## Validation Datasets

### CFP-FP (500 ids/7K images/7K pairs)[12]

### AgeDB-30 (570 ids/12,240 images/6K pairs)[13,6]

### LFW (5749 ids/13233 images/6K pairs)[14]

### CALFW (5749 ids/13233 images/6K pairs)[15]

### CPLFW (5749 ids/13233 images/6K pairs)[16]

## Image Test Datasets

### MegaFace

testsuite: [GDrive](https://drive.google.com/file/d/1KBwp0U9oZgZj7SYDXRxUnnH7Lwvd9XMy/view?usp=sharing)

### IJB (IJB-B, IJB-C)

testsuite: [GDrive](https://drive.google.com/file/d/1aC4zf2Bn0xCVH_ZtEuQipR2JvRb1bf8o/view?usp=sharing)

### TrillionPairs

### NIST

## Video Test Datasets

### YTF

### IQIYI

## Reference

[1] Dong Yi, Zhen Lei, Shengcai Liao, Stan Z. Li. Learning Face Representation from Scratch. arXiv:1411.7923, 2014.

[2] Ziwei Liu, Ping Luo, Xiaogang Wang, Xiaoou Tang. Deep Learning Face Attributes in the Wild, ICCV, 2015.

[3] Bansal Ankan, Nanduri Anirudh, Castillo Carlos D, Ranjan Rajeev, Chellappa, Rama. UMDFaces: An Annotated Face Dataset for Training Deep Networks, arXiv:1611.01484v2, 2016.

[4] Qiong Cao, Li Shen, Weidi Xie, Omkar M. Parkhi, Andrew Zisserman. VGGFace2: A dataset for recognising faces across pose and age. FG, 2018.

[5] Yandong Guo, Lei Zhang, Yuxiao Hu, Xiaodong He, Jianfeng Gao. Ms-celeb-1m: A dataset and benchmark for large-scale face recognition. ECCV, 2016.

[6] Jiankang Deng, Yuxiang Zhou, Stefanos Zafeiriou. Marginal loss for deep face recognition, CVPRW, 2017.

[7] Jiankang Deng, Jia Guo, Stefanos Zafeiriou. Arcface: Additive angular margin loss for deep face recognition, arXiv:1801.07698, 2018.

[8] [http://trillionpairs.deepglint.com/](http://trillionpairs.deepglint.com/)

[9] Wang Fei, Chen Liren, Li Cheng, Huang Shiyao, Chen Yanjie, Qian Chen, Loy, Chen Change. The Devil of Face Recognition is in the Noise, ECCV, 2018.

[10] Cao Jiajiong, Li Yingming, Zhang Zhongfei, Celeb-500K: A Large Training Dataset for Face Recognition, ICIP, 2018.

[11] Nech Aaron, Kemelmacher-Shlizerman Ira, Level Playing Field For Million Scale Face Recognition, CVPR, 2017.
 
[12] Sengupta Soumyadip, Chen Jun-Cheng, Castillo Carlos, Patel Vishal M, Chellappa Rama, Jacobs David W,
  Frontal to profile face verification in the wild, WACV, 2016.

[13] Moschoglou, Stylianos and Papaioannou, Athanasios and Sagonas, Christos and Deng, Jiankang and Kotsia, Irene and Zafeiriou, Stefanos, Agedb: the first manually collected, in-the-wild age database, CVPRW, 2017.

[14] Gary B. Huang, Manu Ramesh, Tamara Berg, and Erik Learned-Miller.
Labeled Faces in the Wild: A Database for Studying Face Recognition in Unconstrained Environments, 2007.

[15] Zheng Tianyue, Deng Weihong, Hu Jiani, Cross-age lfw: A database for studying cross-age face recognition in unconstrained environments, arXiv:1708.08197, 2017.

[16] Zheng, Tianyue, and Weihong Deng. Cross-Pose LFW: A Database for Studying Cross-Pose Face Recognition in Unconstrained Environments, 2018.

[17] An, Xiang and Zhu, Xuhan and Xiao, Yang and Wu, Lan and Zhang, Ming and Gao, Yuan and Qin, Bin and Zhang, Debing and Fu Ying. Partial FC: Training 10 Million Identities on a Single Machine, arxiv:2010.05222, 2020.

[18] Zheng Zhu, Guan Huang, Jiankang Deng, Yun Ye, Junjie Huang, Xinze Chen, Jiagang Zhu, Tian Yang, Jiwen Lu, Dalong Du, Jie Zhou. WebFace260M: A Benchmark Unveiling the Power of Million-scale Deep Face Recognition

[19] Gwangbin Bae, Martin de La Gorce, Tadas Baltrusaitis, Charlie Hewitt, Dong Chen, Julien Valentin, Roberto Cipolla, Jingjing Shen. DigiFace-1M: 1 Million Digital Face Images for Face Recognition. WACV 2023