# Expression FID (EFID) 计算模块

## 概述

该模块用于计算真实视频和生成视频在脸部表情特征上的FID分数。根据论文描述："First, we extract expression parameters using face reconstruction network, and then we compute the FID of these expression parameters to quantitatively measure the divergence between the generated expressions and the ground truth."

## 工作原理

1. **视频抽帧**: 从真实视频和生成视频中按指定比例提取帧
2. **人脸检测**: 检测每帧中的人脸并提取关键点（支持MediaPipe或虚拟关键点）
3. **表情特征提取**: 使用Deep3DFaceRecon_pytorch提取64维表情参数
4. **FID计算**: 计算真实表情参数和生成表情参数的Fréchet距离

## 环境要求

### 基础依赖
```bash
pip install opencv-python torch torchvision numpy scipy pillow mediapipe
```

### Deep3DFaceRecon_pytorch环境
该模块依赖于 `Deep3DFaceRecon_pytorch` 子模块，需要：

1. 确保已下载BFM模型文件到 `Deep3DFaceRecon_pytorch/BFM/` 目录
2. 确保已下载预训练模型到 `Deep3DFaceRecon_pytorch/checkpoints/` 目录
3. 安装Deep3DFaceRecon_pytorch的所有依赖

参考 `Deep3DFaceRecon_pytorch/environment.yml` 和 `Deep3DFaceRecon_pytorch/README.md` 进行环境配置。

## 使用方法

### 命令行调用
```bash
python src/utils/benchmark/calculate_efid.py \
    --real_videos_dir /path/to/real/videos \
    --generated_videos_dir /path/to/generated/videos \
    --sample_rate 0.1 \
    --device cuda:0 \
    --batch_size 8 \
    --max_frames_per_video 100 \
    --output_file efid_results.json
```

### 参数说明
- `--real_videos_dir`: 真实视频文件夹路径（必需）
- `--generated_videos_dir`: 生成视频文件夹路径（必需）
- `--sample_rate`: 抽帧比例，默认0.1（每10帧取1帧）
- `--device`: 计算设备，默认'cuda:0'
- `--batch_size`: 批处理大小，默认8
- `--max_frames_per_video`: 每个视频的最大帧数，默认100
- `--output_file`: 结果输出文件路径（可选）

### 子进程调用示例
```python
import subprocess
import json

# 调用EFID计算
result = subprocess.run([
    'python', 'src/utils/benchmark/calculate_efid.py',
    '--real_videos_dir', '/path/to/real/videos',
    '--generated_videos_dir', '/path/to/generated/videos',
    '--sample_rate', '0.1',
    '--device', 'cuda:0',
    '--output_file', 'efid_results.json'
], capture_output=True, text=True)

if result.returncode == 0:
    # 读取结果
    with open('efid_results.json', 'r') as f:
        efid_data = json.load(f)
    print(f"EFID Score: {efid_data['efid_score']}")
else:
    print(f"Error: {result.stderr}")
```

## 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- FLV (.flv)
- WMV (.wmv)

模块会递归搜索指定目录下的所有支持格式的视频文件。

## 输出格式

### 控制台输出
```
找到 10 个视频文件
从 /path/to/video1.mp4 提取了 20 帧
...
正在设置Deep3DFaceRecon_pytorch模型...
从真实视频中提取帧...
真实视频帧数: 200, 生成视频帧数: 180
保存帧到临时目录...
从真实视频帧中提取表情参数...
从生成视频帧中提取表情参数...
真实表情参数: (190, 64), 生成表情参数: (170, 64)
计算表情FID...

表情FID (EFID) 分数: 15.2347
```

### JSON输出文件
```json
{
  "efid_score": 15.2347,
  "parameters": {
    "real_videos_dir": "/path/to/real/videos",
    "generated_videos_dir": "/path/to/generated/videos",
    "sample_rate": 0.1,
    "device": "cuda:0",
    "batch_size": 8,
    "max_frames_per_video": 100
  }
}
```

## 注意事项

1. **GPU内存**: 确保GPU有足够内存处理批量推理，可调整`batch_size`参数
2. **计算时间**: 表情特征提取需要一定时间，特别是大量视频时
3. **面部检测**: 模块会尝试使用MediaPipe进行人脸检测，如果检测失败会使用虚拟关键点
4. **数据质量**: 确保视频中包含清晰可见的人脸，否则可能影响结果准确性
5. **样本数量**: 建议每组至少有50个以上的表情参数样本以获得稳定的FID分数

## 故障排除

### 常见错误

1. **模型加载失败**
   - 检查Deep3DFaceRecon_pytorch目录是否存在
   - 确认BFM模型文件是否下载完整
   - 验证预训练权重文件路径

2. **CUDA内存不足**
   - 减小batch_size参数
   - 降低max_frames_per_video参数
   - 使用CPU计算（设置device为'cpu'）

3. **无法检测到人脸**
   - 确保视频质量较好
   - 检查MediaPipe是否正确安装
   - 模块会自动回退到虚拟关键点方法

4. **FID计算失败**
   - 确保每组有足够的样本（至少2个）
   - 检查表情参数是否正确提取
   - 验证scipy.linalg是否可用

## 性能优化建议

1. **GPU优化**: 使用CUDA加速表情特征提取
2. **批处理**: 适当增加batch_size以提高处理效率
3. **抽帧策略**: 根据视频内容调整sample_rate
4. **并行处理**: 对于大量视频，可考虑多进程并行处理

## 技术细节

### 表情参数
- 维度: 64维
- 范围: 基于3DMM（3D Morphable Model）的表情基础空间
- 提取方法: Deep3DFaceRecon_pytorch网络输出的80-144维系数

### FID计算
使用标准的Fréchet距离公式：
```
FID = ||μ₁ - μ₂||² + Tr(Σ₁ + Σ₂ - 2√(Σ₁Σ₂))
```
其中μ和Σ分别是特征的均值和协方差矩阵。 