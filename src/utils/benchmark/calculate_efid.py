#!/usr/bin/env python3
"""
Expression FID (EFID) 计算脚本

使用方法:
python calculate_efid.py --real_videos_dir /path/to/real/videos \
                        --generated_videos_dir /path/to/generated/videos \
                        --sample_rate 0.1 \
                        --device cuda:0 \
                        --batch_size 8

注意：这个模块运行在独立的conda环境中，需要安装Deep3DFaceRecon_pytorch的依赖。
"""

import os
import sys
import cv2
import torch
import numpy as np
import argparse
import shutil
import glob
import datetime
from typing import List, Optional
import json

# 添加Deep3DFaceRecon_pytorch路径
script_dir = os.path.dirname(os.path.abspath(__file__))
deep3d_path = os.path.join(script_dir, 'Deep3DFaceRecon_pytorch')
sys.path.insert(0, deep3d_path)

# 导入Deep3DFaceRecon_pytorch模块
from options.test_options import TestOptions
from models import create_model
from util.preprocess import align_img
from util.load_mats import load_lm3d
from PIL import Image


def extract_frames_from_video(video_path: str, sample_rate: float = 0.1, max_frames: int = 100) -> List[np.ndarray]:
    """
    从单个视频中提取帧
    
    Args:
        video_path: 视频文件路径
        sample_rate: 抽帧比例（0.1表示每10帧取1帧）
        max_frames: 最大帧数限制
        
    Returns:
        List[np.ndarray]: 提取的帧列表
    """
    frames = []
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"警告: 无法打开视频文件 {video_path}")
        return frames
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames == 0:
        cap.release()
        return frames
    
    # 根据抽帧比例计算需要提取的帧
    step = max(1, int(1.0 / sample_rate))
    target_frames = min(max_frames, total_frames // step)
    
    frame_indices = np.linspace(0, total_frames - 1, target_frames, dtype=int)
    
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            # BGR转RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame)
    
    cap.release()
    return frames


def extract_frames_from_videos(video_dir: str, sample_rate: float = 0.1, max_frames_per_video: int = 100) -> List[np.ndarray]:
    """
    从视频文件夹中提取所有帧
    
    Args:
        video_dir: 视频文件夹路径
        sample_rate: 抽帧比例
        max_frames_per_video: 每个视频的最大帧数
        
    Returns:
        List[np.ndarray]: 所有提取的帧
    """
    all_frames = []
    video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.flv', '*.wmv']
    
    video_files = []
    for ext in video_extensions:
        video_files.extend(glob.glob(os.path.join(video_dir, ext)))
        video_files.extend(glob.glob(os.path.join(video_dir, '**', ext), recursive=True))
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    for video_path in video_files:
        frames = extract_frames_from_video(video_path, sample_rate, max_frames_per_video)
        all_frames.extend(frames)
        print(f"从 {video_path} 提取了 {len(frames)} 帧")
    
    return all_frames


def save_frames_to_temp_dir(frames: List[np.ndarray], temp_dir: str) -> List[str]:
    """
    将帧保存到临时目录
    
    Args:
        frames: 帧列表
        temp_dir: 临时目录路径
        
    Returns:
        List[str]: 保存的图片文件路径列表
    """
    os.makedirs(temp_dir, exist_ok=True)
    image_paths = []
    
    for i, frame in enumerate(frames):
        image_path = os.path.join(temp_dir, f"frame_{i:06d}.jpg")
        pil_image = Image.fromarray(frame)
        pil_image.save(image_path, quality=95)
        image_paths.append(image_path)
    
    return image_paths


def setup_deep3d_model(device: str = "cuda:0"):
    """
    设置Deep3DFaceRecon_pytorch模型
    
    Args:
        device: 计算设备
        
    Returns:
        模型和相关配置
    """
    # 创建空的命令行参数来避免与当前脚本的参数冲突
    cmd_line = ""
    
    # 设置GPU ID
    if device.startswith("cuda:"):
        gpu_id = device.split(':')[1]
    else:
        gpu_id = "0"
    
    # 手动构建命令行参数
    test_args = [
        '--name', 'face_recon',
        '--epoch', 'latest', 
        '--model', 'facerecon',
        '--phase', 'test',
        '--gpu_ids', gpu_id,
        '--checkpoints_dir', os.path.join(script_dir, 'Deep3DFaceRecon_pytorch', 'checkpoints'),
        '--bfm_folder', os.path.join(script_dir, 'Deep3DFaceRecon_pytorch', 'BFM'),
        '--use_ddp', 'False'
    ]
    
    # 使用自定义命令行参数创建选项
    opt = TestOptions(cmd_line=" ".join(test_args)).parse()
    
    # 确保设备设置正确
    opt.device = device
    
    # 创建模型
    model = create_model(opt)
    model.setup(opt)
    model.device = torch.device(device)
    model.parallelize()
    model.eval()
    
    # 加载3D关键点标准
    lm3d_std = load_lm3d(opt.bfm_folder)
    
    return model, opt, lm3d_std


def extract_expression_parameters(image_paths: List[str], model, opt, lm3d_std, 
                                face_detector=None, batch_size: int = 8) -> np.ndarray:
    """
    从图片中提取表情参数
    
    Args:
        image_paths: 图片路径列表
        model: Deep3DFaceRecon_pytorch模型
        opt: 模型选项
        lm3d_std: 3D关键点标准
        face_detector: 人脸检测器（可选）
        batch_size: 批处理大小
        
    Returns:
        np.ndarray: 表情参数矩阵 (N, 64)
    """
    expression_params = []
    
    # 如果没有提供人脸检测器，尝试导入MediaPipe
    if face_detector is None:
        try:
            import mediapipe as mp
            mp_face_detection = mp.solutions.face_detection
            mp_face_mesh = mp.solutions.face_mesh
            face_detection = mp_face_detection.FaceDetection(model_selection=0, min_detection_confidence=0.5)
            face_mesh = mp_face_mesh.FaceMesh(static_image_mode=True, max_num_faces=1, min_detection_confidence=0.5)
        except ImportError:
            print("警告: 无法导入MediaPipe，将使用简单的面部检测方法")
            face_detection = None
            face_mesh = None
    
    for i in range(0, len(image_paths), batch_size):
        batch_paths = image_paths[i:i + batch_size]
        batch_tensors = []
        
        for img_path in batch_paths:
            try:
                # 加载图片
                img = Image.open(img_path).convert('RGB')
                img_np = np.array(img)
                
                # 检测人脸和关键点
                if face_mesh is not None:
                    results = face_mesh.process(img_np)
                    if results.multi_face_landmarks:
                        # 从MediaPipe获取关键点
                        landmarks = results.multi_face_landmarks[0].landmark
                        h, w = img_np.shape[:2]
                        lm = np.array([[lm.x * w, lm.y * h] for lm in landmarks[:68]])
                    else:
                        # 如果没有检测到人脸，使用图片中心区域的虚拟关键点
                        h, w = img_np.shape[:2]
                        lm = create_dummy_landmarks(w, h)
                else:
                    # 使用虚拟关键点
                    h, w = img_np.shape[:2]
                    lm = create_dummy_landmarks(w, h)
                
                # 对齐图片
                lm[:, 1] = h - 1 - lm[:, 1]  # flip y coordinates
                _, aligned_img, aligned_lm, _ = align_img(img, lm, lm3d_std)
                
                # 转换为tensor
                img_tensor = torch.tensor(np.array(aligned_img) / 255., dtype=torch.float32).permute(2, 0, 1).unsqueeze(0)
                lm_tensor = torch.tensor(aligned_lm).unsqueeze(0)
                
                batch_tensors.append((img_tensor, lm_tensor))
                
            except Exception as e:
                print(f"处理图片 {img_path} 时出错: {e}")
                continue
        
        if not batch_tensors:
            continue
        
        # 批处理推理
        for img_tensor, lm_tensor in batch_tensors:
            try:
                data = {
                    'imgs': img_tensor.to(model.device),
                    'lms': lm_tensor.to(model.device)
                }
                
                model.set_input(data)
                model.test()
                
                # 获取输出系数并提取表情参数
                output_coeff = model.net_recon(data['imgs'])
                coeffs_dict = model.facemodel.split_coeff(output_coeff)
                exp_coeffs = coeffs_dict['exp'].detach().cpu().numpy()
                
                expression_params.append(exp_coeffs[0])  # 取第一个样本（batch_size=1）
                
            except Exception as e:
                print(f"模型推理时出错: {e}")
                continue
    
    if face_detection is not None:
        face_detection.close()
    if face_mesh is not None:
        face_mesh.close()
    
    return np.array(expression_params) if expression_params else np.array([]).reshape(0, 64)


def create_dummy_landmarks(width: int, height: int) -> np.ndarray:
    """
    创建虚拟的68个面部关键点（当面部检测失败时使用）
    
    Args:
        width: 图片宽度
        height: 图片高度
        
    Returns:
        np.ndarray: 68个关键点坐标 (68, 2)
    """
    # 创建一个基于图片中心的标准人脸关键点模板
    cx, cy = width // 2, height // 2
    scale = min(width, height) // 4
    
    # 简化的68点模板（基于标准面部比例）
    landmarks = []
    
    # 面部轮廓 (0-16)
    for i in range(17):
        x = cx + (i - 8) * scale // 8
        y = cy + scale // 2
        landmarks.append([x, y])
    
    # 眉毛 (17-26)
    for i in range(10):
        x = cx + (i - 4.5) * scale // 5
        y = cy - scale // 3
        landmarks.append([x, y])
    
    # 鼻子 (27-35)
    for i in range(9):
        x = cx + (i - 4) * scale // 12
        y = cy - scale // 6 + (i // 3) * scale // 6
        landmarks.append([x, y])
    
    # 眼睛 (36-47)
    for i in range(12):
        side = -1 if i < 6 else 1
        idx = i % 6
        x = cx + side * scale // 3 + (idx - 2.5) * scale // 12
        y = cy - scale // 4
        landmarks.append([x, y])
    
    # 嘴巴 (48-67)
    for i in range(20):
        angle = i * 2 * np.pi / 20
        x = cx + scale // 4 * np.cos(angle)
        y = cy + scale // 3 + scale // 6 * np.sin(angle)
        landmarks.append([x, y])
    
    return np.array(landmarks, dtype=np.float32)


def calculate_fid_from_features(features1: np.ndarray, features2: np.ndarray) -> float:
    """
    从特征矩阵计算FID分数
    
    Args:
        features1: 第一组特征 (N1, D)
        features2: 第二组特征 (N2, D)
        
    Returns:
        float: FID分数
    """
    # 计算均值
    mu1, mu2 = np.mean(features1, axis=0), np.mean(features2, axis=0)
    
    # 计算协方差矩阵
    sigma1 = np.cov(features1, rowvar=False)
    sigma2 = np.cov(features2, rowvar=False)
    
    # 计算均值差
    diff = mu1 - mu2
    
    # 计算协方差矩阵的矩阵平方根
    try:
        # 使用SVD计算矩阵平方根
        covmean = scipy.linalg.sqrtm(sigma1.dot(sigma2))
        
        # 检查数值稳定性
        if np.iscomplexobj(covmean):
            covmean = covmean.real
            
    except Exception:
        # 如果计算失败，使用近似方法
        offset = np.eye(sigma1.shape[0]) * 1e-6
        covmean = scipy.linalg.sqrtm((sigma1 + offset).dot(sigma2 + offset))
        if np.iscomplexobj(covmean):
            covmean = covmean.real
    
    # 计算FID
    fid = diff.dot(diff) + np.trace(sigma1) + np.trace(sigma2) - 2 * np.trace(covmean)
    
    return float(fid)


def calculate_efid(real_videos_dir: str, generated_videos_dir: str, 
                  sample_rate: float = 0.1, device: str = "cuda:0", 
                  batch_size: int = 8, max_frames_per_video: int = 100,
                  output_dir: str = None) -> Optional[float]:
    """
    计算表情FID (Expression FID)
    
    Args:
        real_videos_dir: 真实视频文件夹路径
        generated_videos_dir: 生成视频文件夹路径
        sample_rate: 抽帧比例
        device: 计算设备
        batch_size: 批处理大小
        max_frames_per_video: 每个视频的最大帧数
        
    Returns:
        Optional[float]: EFID分数，如果计算失败返回None
    """
    try:
        # 导入scipy（用于FID计算）
        global scipy
        import scipy.linalg
        
        print("正在设置Deep3DFaceRecon_pytorch模型...")
        model, opt, lm3d_std = setup_deep3d_model(device)
        
        # 创建临时目录
        temp_dir_real = os.path.join(output_dir, 'temp_real')
        temp_dir_gen = os.path.join(output_dir, 'temp_gen')
        
        try:
            print("从真实视频中提取帧...")
            real_frames = extract_frames_from_videos(real_videos_dir, sample_rate, max_frames_per_video)
            if len(real_frames) == 0:
                print("错误: 没有从真实视频中提取到帧")
                return None
            
            print("从生成视频中提取帧...")
            gen_frames = extract_frames_from_videos(generated_videos_dir, sample_rate, max_frames_per_video)
            if len(gen_frames) == 0:
                print("错误: 没有从生成视频中提取到帧")
                return None
            
            print(f"真实视频帧数: {len(real_frames)}, 生成视频帧数: {len(gen_frames)}")
            
            # 保存帧到临时目录
            print("保存帧到临时目录...")
            real_image_paths = save_frames_to_temp_dir(real_frames, temp_dir_real)
            gen_image_paths = save_frames_to_temp_dir(gen_frames, temp_dir_gen)
            
            # 提取表情参数
            print("从真实视频帧中提取表情参数...")
            real_exp_params = extract_expression_parameters(real_image_paths, model, opt, lm3d_std, batch_size=batch_size)
            
            print("从生成视频帧中提取表情参数...")
            gen_exp_params = extract_expression_parameters(gen_image_paths, model, opt, lm3d_std, batch_size=batch_size)
            
            if len(real_exp_params) == 0 or len(gen_exp_params) == 0:
                print("错误: 无法提取足够的表情参数")
                return None
            
            print(f"真实表情参数: {real_exp_params.shape}, 生成表情参数: {gen_exp_params.shape}")
            
            # 计算FID
            print("计算表情FID...")
            efid_score = calculate_fid_from_features(real_exp_params, gen_exp_params)
            
            return efid_score
            
        finally:
            # 清理临时目录
            if os.path.exists(temp_dir_real):
                shutil.rmtree(temp_dir_real)
            if os.path.exists(temp_dir_gen):
                shutil.rmtree(temp_dir_gen)
    
    except Exception as e:
        print(f"计算EFID时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="计算表情FID (Expression FID)")
    parser.add_argument('--real_videos_dir', type=str, required=True, help='真实视频文件夹路径')
    parser.add_argument('--generated_videos_dir', type=str, required=True, help='生成视频文件夹路径')
    parser.add_argument('--output_dir', type=str, required=True, help='输出结果路径')
    parser.add_argument('--sample_rate', type=float, default=0.1, help='抽帧比例 (默认: 0.1)')
    parser.add_argument('--device', type=str, default='cuda:0', help='计算设备 (默认: cuda:0)')
    parser.add_argument('--batch_size', type=int, default=8, help='批处理大小 (默认: 8)')
    parser.add_argument('--max_frames_per_video', type=int, default=100, help='每个视频的最大帧数 (默认: 100)')
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not os.path.exists(args.real_videos_dir):
        print(f"错误: 真实视频目录不存在: {args.real_videos_dir}")
        return
    
    if not os.path.exists(args.generated_videos_dir):
        print(f"错误: 生成视频目录不存在: {args.generated_videos_dir}")
        return
    
    # 计算EFID
    efid_score = calculate_efid(
        real_videos_dir=args.real_videos_dir,
        generated_videos_dir=args.generated_videos_dir,
        sample_rate=args.sample_rate,
        device=args.device,
        batch_size=args.batch_size,
        max_frames_per_video=args.max_frames_per_video,
        output_dir=args.output_dir
    )
    
    if efid_score is not None:
        print(f"\n表情FID (EFID) 分数: {efid_score:.4f}")
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        result = {
            "efid_score": efid_score,
            "parameters": {
                "real_videos_dir": args.real_videos_dir,
                "generated_videos_dir": args.generated_videos_dir,
                "sample_rate": args.sample_rate,
                "device": args.device,
                "batch_size": args.batch_size,
                "max_frames_per_video": args.max_frames_per_video,
                "timestamp": timestamp
            }
        }
        with open(os.path.join(args.output_dir, f'efid_result_{timestamp}.json'), 'w') as f:
            json.dump(result, f, indent=2)
        print(f"结果已保存到: {os.path.join(args.output_dir, f'efid_result_{timestamp}.json')}")
    else:
        print("计算EFID失败")
        sys.exit(1)


if __name__ == "__main__":
    main() 