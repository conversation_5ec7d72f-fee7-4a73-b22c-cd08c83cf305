#!/bin/bash

experiment_name="sonic-finetune-0726"

CHECKPOINTS_DIR="./output/${experiment_name}/checkpoints"  # checkpoint目录路径
# CHECKPOINTS_DIR="./output/temp_test/checkpoints"  # checkpoint目录路径

# VALIDATION_DATA_DIR="/data/zy/Sonic/dataset/hallo3_exmple_dataset"    # 验证数据集目录路径
VALIDATION_DATA_DIR="/data/zy/Sonic/dataset/CelebV-HQ_dataset"    # 验证数据集目录路径

OUTPUT_DIR="./output/${experiment_name}/benchmark"              # 验证结果输出目录
# OUTPUT_DIR="./output/temp_test/output_0705_train"              # 验证结果输出目录

CONFIG_PATH="config/train/train_sonic_0726.yaml"           # 训练配置文件路径
export CUDA_VISIBLE_DEVICES=2
MAX_SAMPLES=100                                              # 最大验证样本数量
CHECK_INTERVAL=60                                          # 检查间隔(秒)

# 确保输出目录存在
mkdir -p $OUTPUT_DIR

# 运行验证脚本
python valid.py \
    --checkpoints_dir $CHECKPOINTS_DIR \
    --validation_data_dir $VALIDATION_DATA_DIR \
    --output_dir $OUTPUT_DIR \
    --config_path $CONFIG_PATH \
    --device_id 0 \
    --max_samples $MAX_SAMPLES \
    --check_interval $CHECK_INTERVAL \
    --keep_recent_k -1

echo "验证脚本已启动，监控目录: $CHECKPOINTS_DIR"
echo "验证结果将保存到: $OUTPUT_DIR"
echo "使用 Ctrl+C 停止监控"

# ==================== 重新计算指标功能说明 ====================
# 
# 如果你需要重新计算被删除的checkpoint的指标，可以使用以下方式：
# 
# 1. 单个checkpoint重新计算（假设checkpoint名称为checkpoint-1000）：
#    python valid.py \
#        --checkpoints_dir $CHECKPOINTS_DIR \
#        --validation_data_dir $VALIDATION_DATA_DIR \
#        --output_dir $OUTPUT_DIR \
#        --config_path $CONFIG_PATH \
#        --device_id 0 \
#        --max_samples $MAX_SAMPLES \
#        --recalculate_metrics \
#        --checkpoint_name checkpoint-1000 \
#        --videos_dir $OUTPUT_DIR/checkpoint-1000/generated_videos
#
# 2. 批量重新计算多个checkpoint的指标：
#    创建一个临时脚本 recalculate_batch.sh：
#    
#    #!/bin/bash
#    CHECKPOINTS=("checkpoint-1000" "checkpoint-2000" "checkpoint-3000")
#    
#    for checkpoint in "${CHECKPOINTS[@]}"; do
#        echo "重新计算 $checkpoint 的指标..."
#        python valid.py \
#            --checkpoints_dir $CHECKPOINTS_DIR \
#            --validation_data_dir $VALIDATION_DATA_DIR \
#            --output_dir $OUTPUT_DIR \
#            --config_path $CONFIG_PATH \
#            --device_id 0 \
#            --max_samples $MAX_SAMPLES \
#            --recalculate_metrics \
#            --checkpoint_name $checkpoint \
#            --videos_dir $OUTPUT_DIR/$checkpoint/generated_videos
#    done
#
# 注意：
# - 确保生成的视频文件还存在于 $OUTPUT_DIR/$checkpoint/generated_videos 目录中
# - 重新计算的结果会自动保存到checkpoint目录和summary文件中
# - 重新计算的结果会标记为 "recalculated": true 